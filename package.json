{"name": "registry", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "registry:build": "node ~/code/shadcn/ui/packages/shadcn/dist/index.js build"}, "dependencies": {"@radix-ui/react-label": "^2.1.4", "@radix-ui/react-slot": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "next": "15.3.1", "react": "19.1.0", "react-dom": "19.1.0", "shadcn": "^2.4.1", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}, "pnpm": {"overrides": {"@types/react": "19.1.2", "@types/react-dom": "19.1.2"}}}