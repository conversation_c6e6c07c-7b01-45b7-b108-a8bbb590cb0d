.login-container {
  --primary-color: #111111;
  --primary-hover: #484747;
  --error-color: #ef4444;
  --text-color: #1f2937;
  --text-light: #6b7280;
  --border-color: #e5e7eb;
  --background-light: #f9fafb;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);

  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  background-color: var(--background-light);
  width: 100%;
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
}

.login-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.login-subtitle {
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
  color: var(--text-light);
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.login-error {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: rgba(239, 68, 68, 0.1);
  border-left: 3px solid var(--error-color);
  color: var(--error-color);
  font-size: 0.875rem;
  border-radius: 4px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color);
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.15s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group input::placeholder {
  color: var(--text-light);
}

.form-actions {
  margin-top: 0.5rem;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.login-button:hover {
  background-color: var(--primary-hover);
}

.login-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3);
}

.form-footer {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.forgot-password {
  font-size: 0.875rem;
  color: var(--primary-color);
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .login-card {
    padding: 1.5rem;
  }
}
