{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "example-with-css", "type": "registry:component", "title": "Example with CSS", "description": "A login form with a CSS file.", "files": [{"path": "registry/new-york/blocks/example-with-css/example-card.tsx", "content": "import \"./example-card.css\"\n\nexport function ExampleCard() {\n  return (\n    <div className=\"login-container\">\n      <div className=\"login-card\">\n        <h1 className=\"login-title\">Login</h1>\n        <p className=\"login-subtitle\">\n          Please enter your credentials to continue\n        </p>\n        <form className=\"login-form\">\n          <div className=\"form-group\">\n            <label htmlFor=\"field-email\">Email</label>\n            <input\n              id=\"field-email\"\n              type=\"email\"\n              placeholder=\"Enter your email\"\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label htmlFor=\"field-password\">Password</label>\n            <input\n              id=\"field-password\"\n              type=\"password\"\n              placeholder=\"Enter your password\"\n              required\n            />\n          </div>\n          <div className=\"form-actions\">\n            <button type=\"submit\" className=\"login-button\">\n              Sign In\n            </button>\n          </div>\n          <div className=\"form-footer\">\n            <a href=\"#\" className=\"forgot-password\">\n              Forgot password?\n            </a>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/new-york/blocks/example-with-css/example-card.css", "content": ".login-container {\n  --primary-color: #111111;\n  --primary-hover: #484747;\n  --error-color: #ef4444;\n  --text-color: #1f2937;\n  --text-light: #6b7280;\n  --border-color: #e5e7eb;\n  --background-light: #f9fafb;\n  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),\n    0 2px 4px -1px rgba(0, 0, 0, 0.06);\n\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 1rem;\n  background-color: var(--background-light);\n  width: 100%;\n}\n\n.login-card {\n  width: 100%;\n  max-width: 400px;\n  padding: 2rem;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: var(--card-shadow);\n}\n\n.login-title {\n  margin: 0 0 0.5rem 0;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: var(--text-color);\n}\n\n.login-subtitle {\n  margin: 0 0 1.5rem 0;\n  font-size: 0.875rem;\n  color: var(--text-light);\n}\n\n.login-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1.25rem;\n}\n\n.login-error {\n  padding: 0.75rem;\n  margin-bottom: 0.5rem;\n  background-color: rgba(239, 68, 68, 0.1);\n  border-left: 3px solid var(--error-color);\n  color: var(--error-color);\n  font-size: 0.875rem;\n  border-radius: 4px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-group label {\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-color);\n}\n\n.form-group input {\n  padding: 0.75rem;\n  border: 1px solid var(--border-color);\n  border-radius: 4px;\n  font-size: 1rem;\n  transition: border-color 0.15s ease;\n}\n\n.form-group input:focus {\n  outline: none;\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\n}\n\n.form-group input::placeholder {\n  color: var(--text-light);\n}\n\n.form-actions {\n  margin-top: 0.5rem;\n}\n\n.login-button {\n  width: 100%;\n  padding: 0.75rem;\n  background-color: var(--primary-color);\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.15s ease;\n}\n\n.login-button:hover {\n  background-color: var(--primary-hover);\n}\n\n.login-button:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3);\n}\n\n.form-footer {\n  display: flex;\n  justify-content: center;\n  margin-top: 1rem;\n}\n\n.forgot-password {\n  font-size: 0.875rem;\n  color: var(--primary-color);\n  text-decoration: none;\n}\n\n.forgot-password:hover {\n  text-decoration: underline;\n}\n\n@media (max-width: 480px) {\n  .login-card {\n    padding: 1.5rem;\n  }\n}\n", "type": "registry:component"}]}